import { useCallback, useEffect, useState } from 'react';

interface UsePersistentTabStateOptions<T> {
  key: string;
  defaultValue: T;
  resetTrigger?: string | number; // When this changes, reset to default
}

/**
 * Custom hook to persist tab state in sessionStorage
 * This preserves tab selection across modal opens/closes but resets when the trigger changes
 */
export function usePersistentTabState<T extends string>({
  key,
  defaultValue,
  resetTrigger,
}: UsePersistentTabStateOptions<T>) {
  const [selectedTab, setSelectedTabState] = useState<T>(defaultValue);

  // Load initial state from sessionStorage
  useEffect(() => {
    try {
      const stored = sessionStorage.getItem(key);
      if (stored) {
        setSelectedTabState(stored as T);
      }
    } catch (error) {
      console.warn('Failed to load tab state from sessionStorage:', error);
    }
  }, [key]);

  // Reset tab when trigger changes (e.g., when candidateId changes)
  useEffect(() => {
    if (resetTrigger !== undefined) {
      setSelectedTabState(defaultValue);
      try {
        sessionStorage.setItem(key, defaultValue);
      } catch (error) {
        console.warn('Failed to save tab state to sessionStorage:', error);
      }
    }
  }, [resetTrigger, defaultValue, key]);

  const setSelectedTab = useCallback(
    (tab: T) => {
      setSelectedTabState(tab);
      try {
        sessionStorage.setItem(key, tab);
      } catch (error) {
        console.warn('Failed to save tab state to sessionStorage:', error);
      }
    },
    [key]
  );

  return [selectedTab, setSelectedTab] as const;
}
