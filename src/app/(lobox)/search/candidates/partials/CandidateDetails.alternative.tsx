// Alternative implementation using sessionStorage instead of URL parameters
// This is just for reference - you can choose which approach you prefer

import { useQueryClient } from '@tanstack/react-query';
import dynamic from 'next/dynamic';
import React, { useCallback, type FC } from 'react';
import { usePersistentTabState } from '@shared/utils/hooks/usePersistentTabState';
// ... other imports

type CandidateDetailsTabsNames =
  | 'about'
  | 'jobs'
  | 'availability'
  | 'similar'
  | 'activities'
  | 'insights';

interface CandidateDetailsProps {
  candidateId?: string | null;
  parentLoading?: boolean;
  currentIndex?: number;
  totalElements?: number;
}

const CandidateDetails: FC<CandidateDetailsProps> = ({
  candidateId,
  parentLoading,
  currentIndex,
  totalElements,
}) => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();

  // Use persistent tab state that resets when candidateId changes
  const [selectedTab, setSelectedTab] = usePersistentTabState({
    key: 'candidateDetailsTab',
    defaultValue: 'about' as CandidateDetailsTabsNames,
    resetTrigger: candidateId, // Reset tab when candidate changes
  });

  const { data: candidate, isLoading } = useReactQuery<CandidateFormData>({
    action: {
      apiFunc: getCandidateById,
      key: [QueryKeys.getCandidate, candidateId],
      params: {
        id: candidateId,
        containsLastActivity: true,
      },
      spreadParams: true,
    },
    config: {
      enabled: !!candidateId,
    },
  });

  const handleOpenManager = useCallback(
    (tab?: CandidateManagerTabkeys) => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          currentIndex,
          tab,
          totalElements,
          entity: 'currentEntityId',
          apiFunc: searchCandidateItem,
        },
      });
    },
    [appDispatch, totalElements]
  );

  // ... rest of the component remains the same
  
  return (
    <>
      <CandidateCard
        // ... card props
        footer={
          <Tabs
            activePath={selectedTab}
            onChangeTab={setSelectedTab}
            styles={{
              tabsRoot: classes.tabsRoot,
            }}
            tabs={tabs.map((tab) => ({ ...tab, title: t(tab.title) }))}
          />
        }
      >
        {/* ... card content */}
      </CandidateCard>
      <Panels candidate={candidate} tab={selectedTab} />
    </>
  );
};

export default CandidateDetails;
